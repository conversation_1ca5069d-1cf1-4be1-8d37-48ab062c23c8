<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function receptionists(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Receptionist::class);
    }

    public function gifts(): BelongsToMany
    {
        return $this->belongsToMany(Gift::class);
    }
}
