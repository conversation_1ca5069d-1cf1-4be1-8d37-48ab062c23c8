<?php

namespace App\Services;

use App\Models\Receptionist;
use App\Models\Setting;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\Storage;

class BadgeService
{
    protected ImageManager $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Generate QR code for receptionist
     */
    public function generateQrCode(Receptionist $receptionist): string
    {
        $landingUrl = $receptionist->landing_url;
        
        // Generate QR code as PNG
        $qrCode = QrCode::format('png')
            ->size(300)
            ->margin(2)
            ->generate($landingUrl);

        // Save QR code to storage
        $filename = "qr-codes/receptionist-{$receptionist->id}.png";
        Storage::disk('public')->put($filename, $qrCode);

        return $filename;
    }

    /**
     * Generate badge using pure GD (credit card size: 85.6mm × 53.98mm)
     * Portrait orientation: 638px × 1012px
     */
    public function generateBadge(Receptionist $receptionist): string
    {
        try {
            // Credit card dimensions at 300 DPI - Portrait orientation
            $width = 638;   // 53.98mm
            $height = 1012; // 85.60mm

            // Create blank image with white background
            $image = imagecreatetruecolor($width, $height);
            $white = imagecolorallocate($image, 255, 255, 255);
            $black = imagecolorallocate($image, 0, 0, 0);
            $gray = imagecolorallocate($image, 102, 102, 102);
            $lightgray = imagecolorallocate($image, 200, 200, 200);
            $darkgray = imagecolorallocate($image, 51, 51, 51);
            $blue = imagecolorallocate($image, 0, 100, 200);

            imagefill($image, 0, 0, $white);

            // Calculate vertical spacing for portrait layout with logo
            $topMargin = 80;
            $logoHeight = 60;
            $logoY = $topMargin;
            $messageY = $logoY + $logoHeight + 40;
            $qrSize = 350; // Slightly smaller to accommodate logo
            $qrY = $messageY + 80;
            $nameY = $qrY + $qrSize + 50;

            // Calculate horizontal center
            $centerX = $width / 2;
            $qrX = ($width - $qrSize) / 2;

            // Add logo at the top if available
            $this->addLogo($image, $centerX, $logoY, $logoHeight);

            // Add customer message at top
            $this->addCenteredText($image, 'Dear Customer,', $centerX, $messageY, 4, $darkgray);
            $this->addCenteredText($image, 'Please scan the QR code below', $centerX, $messageY + 40, 3, $black);

            // Generate QR code using online service
            $qrImage = $this->generateQrCodeImage($receptionist->landing_url);
            if ($qrImage) {
                // Resize QR code to desired size
                $qrResized = imagecreatetruecolor($qrSize, $qrSize);
                imagecopyresampled($qrResized, $qrImage, 0, 0, 0, 0, $qrSize, $qrSize, imagesx($qrImage), imagesy($qrImage));

                // Place QR code centered
                imagecopy($image, $qrResized, $qrX, $qrY, 0, 0, $qrSize, $qrSize);

                imagedestroy($qrImage);
                imagedestroy($qrResized);
            } else {
                // Fallback to placeholder if QR generation fails
                imagefilledrectangle($image, $qrX, $qrY, $qrX + $qrSize, $qrY + $qrSize, $lightgray);
                imagerectangle($image, $qrX, $qrY, $qrX + $qrSize, $qrY + $qrSize, $darkgray);
                $this->addCenteredText($image, 'QR CODE', $centerX, $qrY + $qrSize / 2 - 20, 4, $black);
                $this->addCenteredText($image, 'PLACEHOLDER', $centerX, $qrY + $qrSize / 2 + 20, 3, $gray);
            }

            // Add receptionist full name below QR code
            $this->addCenteredText($image, $receptionist->full_name, $centerX, $nameY, 5, $black);

            // Add department name below receptionist name
            $this->addCenteredText($image, $receptionist->department->name, $centerX, $nameY + 50, 4, $blue);

            // Add border
            imagerectangle($image, 0, 0, $width - 1, $height - 1, $lightgray);

            // Save badge
            $filename = "badges/receptionist-{$receptionist->id}-badge.png";
            $badgePath = Storage::disk('public')->path($filename);

            // Ensure directory exists
            Storage::disk('public')->makeDirectory('badges');

            imagepng($image, $badgePath);
            imagedestroy($image);

            return $filename;

        } catch (\Exception $e) {
            \Log::error('Badge generation failed: ' . $e->getMessage());
            throw new \Exception('Failed to generate badge: ' . $e->getMessage());
        }
    }

    /**
     * Get badge download response
     */
    public function downloadBadge(Receptionist $receptionist)
    {
        $badgePath = $this->generateBadge($receptionist);
        $fullPath = Storage::disk('public')->path($badgePath);

        if (!file_exists($fullPath)) {
            abort(404, 'Badge not found');
        }

        $filename = "badge-{$receptionist->full_name}-{$receptionist->unique_url}.png";
        $filename = preg_replace('/[^a-zA-Z0-9\-_\.]/', '-', $filename);

        return response()->download($fullPath, $filename);
    }

    private function addCenteredText($image, $text, $centerX, $y, $fontSize, $color)
    {
        // Use built-in GD fonts only (no TTF to avoid font file dependencies)
        $textWidth = strlen($text) * imagefontwidth($fontSize);
        $x = $centerX - ($textWidth / 2);
        imagestring($image, $fontSize, $x, $y - 15, $text, $color);
    }

    private function generateQrCodeImage($url)
    {
        try {
            // Use QR Server API (free online service)
            $qrUrl = "https://api.qrserver.com/v1/create-qr-code/?size=400x400&data=" . urlencode($url);

            // Get QR code image data
            $qrData = @file_get_contents($qrUrl);

            if ($qrData === false) {
                return null;
            }

            // Create image from string
            $qrImage = @imagecreatefromstring($qrData);

            return $qrImage;

        } catch (\Exception $e) {
            \Log::warning('QR code generation failed, using placeholder: ' . $e->getMessage());
            return null;
        }
    }

    private function addLogo($image, $centerX, $logoY, $logoHeight)
    {
        try {
            // Get logo path from settings
            $logoPath = Setting::get('brand_logo');
            if (!$logoPath) {
                return; // No logo configured
            }

            // Determine if it's an uploaded file or static asset
            $logoImageData = null;

            if (str_starts_with($logoPath, 'assets/')) {
                // Try storage first (uploaded file)
                if (Storage::disk('public')->exists($logoPath)) {
                    $logoImageData = Storage::disk('public')->get($logoPath);
                } else {
                    // Try public directory (static file)
                    $publicPath = public_path($logoPath);
                    if (file_exists($publicPath)) {
                        $logoImageData = file_get_contents($publicPath);
                    }
                }
            } else {
                // Direct path
                if (file_exists($logoPath)) {
                    $logoImageData = file_get_contents($logoPath);
                }
            }

            if (!$logoImageData) {
                return; // Logo file not found
            }

            // Create image from logo data
            $logoImage = @imagecreatefromstring($logoImageData);
            if (!$logoImage) {
                return; // Failed to create image
            }

            // Calculate logo dimensions maintaining aspect ratio
            $originalWidth = imagesx($logoImage);
            $originalHeight = imagesy($logoImage);
            $aspectRatio = $originalWidth / $originalHeight;

            $logoWidth = $logoHeight * $aspectRatio;
            $logoX = $centerX - ($logoWidth / 2);

            // Create resized logo
            $resizedLogo = imagecreatetruecolor($logoWidth, $logoHeight);

            // Handle transparency for PNG images
            imagealphablending($resizedLogo, false);
            imagesavealpha($resizedLogo, true);
            $transparent = imagecolorallocatealpha($resizedLogo, 255, 255, 255, 127);
            imagefill($resizedLogo, 0, 0, $transparent);
            imagealphablending($resizedLogo, true);

            // Resize logo
            imagecopyresampled(
                $resizedLogo, $logoImage,
                0, 0, 0, 0,
                $logoWidth, $logoHeight,
                $originalWidth, $originalHeight
            );

            // Place logo on badge
            imagecopy($image, $resizedLogo, $logoX, $logoY, 0, 0, $logoWidth, $logoHeight);

            // Clean up
            imagedestroy($logoImage);
            imagedestroy($resizedLogo);

        } catch (\Exception $e) {
            \Log::warning('Logo placement failed: ' . $e->getMessage());
            // Continue without logo if there's an error
        }
    }
}
