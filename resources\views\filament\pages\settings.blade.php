<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Page Header --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
                        ⚙️ System Settings
                    </h2>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Configure your application branding, theme, and layout preferences
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        🟢 Active
                    </span>
                </div>
            </div>
        </div>

        {{-- Settings Form --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
            <form wire:submit="save">
                {{ $this->form }}

                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            💡 Changes will be applied immediately after saving
                        </div>
                        <div class="flex space-x-3">
                            <x-filament::button
                                type="button"
                                color="gray"
                                outlined
                                wire:click="mount"
                            >
                                Reset
                            </x-filament::button>
                            <x-filament::button
                                type="submit"
                                color="primary"
                            >
                                Save Settings
                            </x-filament::button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        {{-- Current Settings Preview --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                🎨 Current Settings Preview
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Brand Name</div>
                    <div class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        {{ \App\Models\Setting::get('brand_name', 'Review Tracker') }}
                    </div>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Primary Color</div>
                    <div class="flex items-center space-x-2">
                        <div
                            class="w-6 h-6 rounded-full border border-gray-300"
                            style="background-color: {{ \App\Models\Setting::get('primary_color', '#92541B') }}"
                        ></div>
                        <span class="text-sm font-mono text-gray-900 dark:text-gray-100">
                            {{ \App\Models\Setting::get('primary_color', '#92541B') }}
                        </span>
                    </div>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Navigation</div>
                    <div class="text-sm text-gray-900 dark:text-gray-100">
                        {{ ucfirst(\App\Models\Setting::get('navigation_type', 'sidebar')) }} Layout
                    </div>
                </div>
            </div>
        </div>

        {{-- Help Section --}}
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
                📚 Settings Help
            </h3>
            <div class="space-y-2 text-sm text-blue-800 dark:text-blue-200">
                <p><strong>Brand Logo:</strong> Upload SVG files for best quality. Recommended size: 120x40px</p>
                <p><strong>Favicon:</strong> Use ICO format for best browser compatibility. Size: 32x32px or 16x16px</p>
                <p><strong>Primary Color:</strong> This color will be used for buttons, links, and highlights</p>
                <p><strong>Navigation:</strong> Sidebar is recommended for desktop, top navigation for mobile-first designs</p>
            </div>
        </div>
    </div>
</x-filament-panels::page>
