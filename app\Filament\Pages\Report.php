<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use App\Models\Department;
use App\Models\Receptionist;
use App\Models\Visit;
use App\Models\Click;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Contracts\View\View;
use App\Models\Platform;
use App\Models\CustomerCount;

class Report extends Page implements HasForms, HasTable
{
    use InteractsWithForms, InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';

    protected static string $view = 'filament.pages.report';

    protected static ?string $title = 'Reports';

    protected static ?string $navigationLabel = 'Reports';

    protected static ?int $navigationSort = 2;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'start_date' => now()->startOfMonth()->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('department_id')
                    ->label('Department')
                    ->placeholder('All Departments')
                    ->options(Department::where('is_active', true)->pluck('name', 'id'))
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function () {
                        $this->updateReceptionistOptions();
                        $this->resetTable();
                        $this->dispatch('refresh-platform-clicks');
                    }),

                Select::make('receptionist_id')
                    ->label('Receptionist')
                    ->placeholder('All Receptionists')
                    ->options(fn () => $this->getReceptionistOptions())
                    ->searchable()
                    ->live()
                    ->afterStateUpdated(function () {
                        $this->resetTable();
                        $this->dispatch('refresh-platform-clicks');
                    }),

                DatePicker::make('start_date')
                    ->label('Start Date')
                    ->default(now()->startOfMonth())
                    //->native(false)
                    //->displayFormat('d/m/Y')
                    ->live()
                    ->afterStateUpdated(function () {
                        $this->resetTable();
                        $this->dispatch('refresh-platform-clicks');
                    }),

                DatePicker::make('end_date')
                    ->label('End Date')
                    ->default(now())
                    //->native(false)
                    //->displayFormat('d/m/Y')
                    ->live()
                    ->afterStateUpdated(function () {
                        $this->resetTable();
                        $this->dispatch('refresh-platform-clicks');
                    }),
            ])
            ->columns(4)
            ->statePath('data');
    }

    protected function getReceptionistOptions(): array
    {
        $query = Receptionist::where('is_active', true);

        if ($this->data['department_id'] ?? null) {
            $query->where('department_id', $this->data['department_id']);
        }

        return $query->pluck('full_name', 'id')->toArray();
    }

    public function updateReceptionistOptions(): void
    {
        $this->data['receptionist_id'] = null;
    }

    public function applyFilters(): void
    {
        // Refresh both tables when filters are applied
        $this->resetTable();
        $this->dispatch('refresh-platform-clicks');
    }

    public function resetFilters(): void
    {
        $this->form->fill([
            'department_id' => null,
            'receptionist_id' => null,
            'start_date' => now()->startOfMonth()->format('Y-m-d'),
            'end_date' => now()->format('Y-m-d'),
        ]);

        // Refresh both tables when filters are reset
        $this->resetTable();
        $this->dispatch('refresh-platform-clicks');
    }

    public function getFilteredData(): array
    {
        return [
            'department_id' => $this->data['department_id'] ?? null,
            'receptionist_id' => $this->data['receptionist_id'] ?? null,
            'start_date' => $this->data['start_date'] ?? null,
            'end_date' => $this->data['end_date'] ?? null,
        ];
    } 

    public function getTotalVisitors(): int
    {
        return $this->getFilteredVisitsQuery()->count();
    }

    public function getTotalClicks(): int
    {
        return $this->getFilteredClicksQuery()->count();
    }

    public function getTopReceptionist(): ?array
    {
        $filters = $this->getFilteredData();

        $query = Receptionist::query()
            ->with(['department'])
            ->withCount([
                'visits' => function ($query) use ($filters) {
                    $this->applyDateFilters($query, $filters);
                },
                'clicks' => function ($query) use ($filters) {
                    $this->applyDateFilters($query, $filters);
                }
            ]);

        // Apply department filter
        if ($filters['department_id']) {
            $query->where('department_id', $filters['department_id']);
        }

        // Apply receptionist filter
        if ($filters['receptionist_id']) {
            $query->where('id', $filters['receptionist_id']);
        }

        $topReceptionist = $query->orderByDesc('visits_count')->first();

        if (!$topReceptionist) {
            return null;
        }

        return [
            'name' => $topReceptionist->full_name,
            'department' => $topReceptionist->department->name ?? 'N/A',
            'visits' => $topReceptionist->visits_count,
            'clicks' => $topReceptionist->clicks_count,
            'conversion_rate' => $topReceptionist->visits_count > 0
                ? round(($topReceptionist->clicks_count / $topReceptionist->visits_count) * 100, 1)
                : 0
        ];
    }

    protected function getFilteredVisitsQuery(): Builder
    {
        $filters = $this->getFilteredData();
        $query = Visit::query();

        // Apply receptionist filter
        if ($filters['receptionist_id']) {
            $query->where('receptionist_id', $filters['receptionist_id']);
        } elseif ($filters['department_id']) {
            // If department is selected but not receptionist, filter by department
            $query->whereHas('receptionist', function ($q) use ($filters) {
                $q->where('department_id', $filters['department_id']);
            });
        }

        // Apply date filters
        $this->applyDateFilters($query, $filters);

        return $query;
    }

    protected function getFilteredClicksQuery(): Builder
    {
        $filters = $this->getFilteredData();
        $query = Click::query();

        // Apply receptionist filter
        if ($filters['receptionist_id']) {
            $query->where('receptionist_id', $filters['receptionist_id']);
        } elseif ($filters['department_id']) {
            // If department is selected but not receptionist, filter by department
            $query->whereHas('receptionist', function ($q) use ($filters) {
                $q->where('department_id', $filters['department_id']);
            });
        }

        // Apply date filters
        $this->applyDateFilters($query, $filters);

        return $query;
    }

    protected function applyDateFilters(Builder $query, array $filters): void
    {
        if ($filters['start_date']) {
            $query->where('created_at', '>=', $filters['start_date'] . ' 00:00:00');
        }

        if ($filters['end_date']) {
            $query->where('created_at', '<=', $filters['end_date'] . ' 23:59:59');
        }
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                TextColumn::make('full_name')
                    ->label('Receptionist')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('department.name')
                    ->label('Department')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('visits_count')
                    ->label('Visitors')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('clicks_count')
                    ->label('Clicks')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('customer_conversion_rate')
                    ->label('Visitor Conversion Rate')
                    ->getStateUsing(function ($record) {
                        $filters = $this->getFilteredData();
                        $startDate = $filters['start_date'] ? \Carbon\Carbon::parse($filters['start_date']) : \Carbon\Carbon::now()->startOfMonth();
                        $endDate = $filters['end_date'] ? \Carbon\Carbon::parse($filters['end_date']) : \Carbon\Carbon::now()->endOfMonth();

                        $conversionRate = $record->getConversionRate($startDate, $endDate);
                        $totalCustomers = $record->getTotalCustomers($startDate, $endDate);

                        if ($totalCustomers == 0) {
                            return 'No data';
                        }

                        return $conversionRate . '%';
                    })
                    ->alignCenter()
                    ->badge()
                    ->color(function ($record) {
                        $filters = $this->getFilteredData();
                        $startDate = $filters['start_date'] ? \Carbon\Carbon::parse($filters['start_date']) : \Carbon\Carbon::now()->startOfMonth();
                        $endDate = $filters['end_date'] ? \Carbon\Carbon::parse($filters['end_date']) : \Carbon\Carbon::now()->endOfMonth();

                        $conversionRate = $record->getConversionRate($startDate, $endDate);
                        $totalCustomers = $record->getTotalCustomers($startDate, $endDate);

                        if ($totalCustomers == 0) return 'gray';

                        if ($conversionRate >= 15) return 'success';
                        if ($conversionRate >= 8) return 'warning';
                        return 'danger';
                    }),

                TextColumn::make('total_customers')
                    ->label('Total Customers')
                    ->getStateUsing(function ($record) {
                        $filters = $this->getFilteredData();
                        $startDate = $filters['start_date'] ? \Carbon\Carbon::parse($filters['start_date']) : \Carbon\Carbon::now()->startOfMonth();
                        $endDate = $filters['end_date'] ? \Carbon\Carbon::parse($filters['end_date']) : \Carbon\Carbon::now()->endOfMonth();

                        return number_format($record->getTotalCustomers($startDate, $endDate));
                    })
                    ->alignCenter()
                    ->toggleable(),

                TextColumn::make('visit_conversion_rate')
                    ->label('Visit Conversion Rate')
                    ->getStateUsing(function ($record) {
                        $visits = $record->visits_count ?? 0;
                        $clicks = $record->clicks_count ?? 0;

                        if ($visits == 0) {
                            return '0%';
                        }

                        return round(($clicks / $visits) * 100, 1) . '%';
                    })
                    ->alignCenter()
                    ->badge()
                    ->color(function ($record) {
                        $visits = $record->visits_count ?? 0;
                        $clicks = $record->clicks_count ?? 0;

                        if ($visits == 0) return 'gray';

                        $rate = ($clicks / $visits) * 100;
                        if ($rate >= 50) return 'success';
                        if ($rate >= 25) return 'warning';
                        return 'danger';
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('visits_count', 'desc')
            ->paginated([10, 25, 50])
            ->poll('30s');
    }

    protected function getTableQuery(): Builder
    {
        $filters = $this->getFilteredData();

        $query = Receptionist::query()
            ->with(['department'])
            ->withCount([
                'visits' => function ($query) use ($filters) {
                    $this->applyDateFilters($query, $filters);
                },
                'clicks' => function ($query) use ($filters) {
                    $this->applyDateFilters($query, $filters);
                }
            ]);

        // Apply department filter
        if ($filters['department_id']) {
            $query->where('department_id', $filters['department_id']);
        }

        // Apply receptionist filter
        if ($filters['receptionist_id']) {
            $query->where('id', $filters['receptionist_id']);
        }

        // Only show active receptionists
        $query->where('is_active', true);

        return $query;
    }

    public function getPlatformClicksData(): array
    {
        $filters = $this->getFilteredData();

        // Get all active platforms
        $platforms = Platform::where('is_active', true)->get();

        // Build the base query for clicks
        $clicksQuery = Click::query()
            ->with(['receptionist.department', 'platform'])
            ->whereHas('receptionist', function ($query) {
                $query->where('is_active', true);
            })
            ->whereHas('platform', function ($query) {
                $query->where('is_active', true);
            });

        // Apply date filters
        $this->applyDateFilters($clicksQuery, $filters);

        // Apply department filter
        if ($filters['department_id']) {
            $clicksQuery->whereHas('receptionist', function ($query) use ($filters) {
                $query->where('department_id', $filters['department_id']);
            });
        }

        // Apply receptionist filter
        if ($filters['receptionist_id']) {
            $clicksQuery->where('receptionist_id', $filters['receptionist_id']);
        }

        // Get clicks grouped by receptionist and platform
        $clicks = $clicksQuery->get()
            ->groupBy('receptionist_id')
            ->map(function ($receptionistClicks) use ($platforms) {
                $receptionist = $receptionistClicks->first()->receptionist;
                $clicksByPlatform = $receptionistClicks->groupBy('platform_id');

                $platformData = [];
                foreach ($platforms as $platform) {
                    $platformClicks = $clicksByPlatform->get($platform->id, collect());
                    $platformData[] = [
                        'platform_name' => $platform->name,
                        'clicks_count' => $platformClicks->count(),
                    ];
                }

                return [
                    'receptionist' => $receptionist,
                    'platforms' => $platformData,
                    'total_clicks' => $receptionistClicks->count(),
                ];
            });

        return $clicks->toArray();
    }

    public function getPlatformClicksTableData(): array
    {
        $platformClicksData = $this->getPlatformClicksData();
        $tableData = [];

        foreach ($platformClicksData as $receptionistData) {
            $receptionist = $receptionistData['receptionist'];
            $platforms = $receptionistData['platforms'];

            // Add a row for each platform for this receptionist
            foreach ($platforms as $index => $platformData) {
                $tableData[] = [
                    'receptionist_name' => $index === 0 ? $receptionist->full_name : '', // Only show name on first row
                    'receptionist_department' => $index === 0 ? $receptionist->department->name : '',
                    'platform_name' => $platformData['platform_name'],
                    'clicks_count' => $platformData['clicks_count'],
                    'is_first_row' => $index === 0,
                    'receptionist_id' => $receptionist->id,
                ];
            }
        }

        return $tableData;
    }
}
