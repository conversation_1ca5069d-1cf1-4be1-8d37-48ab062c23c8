<?php

use App\Http\Controllers\BadgeController;
use App\Http\Controllers\LandingController;
use Illuminate\Support\Facades\Route;

// Badge download route
Route::get('/receptionist/{receptionist}/badge', [BadgeController::class, 'downloadBadge'])
    ->name('receptionist.badge');

// Landing page routes
Route::get('/landing/{unique_url}', [LandingController::class, 'show'])
    ->name('landing.show');

Route::post('/landing/{unique_url}/click', [LandingController::class, 'trackClick'])
    ->name('landing.click');

// Debug route to check logs
Route::get('/debug/logs', function () {
    $logs = file_get_contents(storage_path('logs/laravel.log'));
    $lines = explode("\n", $logs);
    $recentLines = array_slice($lines, -50); // Last 50 lines
    return '<pre>' . implode("\n", $recentLines) . '</pre>';
});
