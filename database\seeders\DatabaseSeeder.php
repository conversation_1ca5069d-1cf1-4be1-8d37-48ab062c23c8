<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Department;
use App\Models\Platform;
use App\Models\Gift;
use App\Models\Receptionist;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create sample departments
        $departments = [
            ['name' => 'Customer Service'],
            ['name' => 'Sales'],
            ['name' => 'Marketing'],
        ];

        foreach ($departments as $dept) {
            Department::create($dept);
        }

        // Create sample platforms
        $platforms = [
            ['name' => 'Google Reviews', 'url' => 'https://g.page/r/YOUR_BUSINESS_ID/review'],
            ['name' => 'Facebook', 'url' => 'https://www.facebook.com/YOUR_PAGE/reviews'],
            ['name' => 'Trustpilot', 'url' => 'https://www.trustpilot.com/review/YOUR_DOMAIN'],
            ['name' => 'Yelp', 'url' => 'https://www.yelp.com/writeareview/biz/YOUR_BUSINESS_ID'],
        ];

        foreach ($platforms as $platform) {
            Platform::create($platform);
        }

        // Create sample gifts (independent of departments)
        $gifts = [
            ['name' => 'Coffee Voucher', 'description' => 'Free coffee from our cafe'],
            ['name' => 'Discount Coupon', 'description' => '10% off your next purchase'],
            ['name' => 'Free Consultation', 'description' => '30-minute free consultation'],
            ['name' => 'Gift Card', 'description' => '$25 gift card'],
            ['name' => 'Premium Service', 'description' => 'Upgrade to premium service'],
            ['name' => 'Company Merchandise', 'description' => 'Branded t-shirt or mug'],
            ['name' => 'VIP Access Pass', 'description' => 'Priority service access'],
            ['name' => 'Digital Gift Card', 'description' => '$50 digital gift card'],
        ];

        $createdGifts = [];
        foreach ($gifts as $gift) {
            $createdGifts[] = Gift::create([
                'name' => $gift['name'],
                'description' => $gift['description'],
            ]);
        }

        // Attach some gifts to departments using the pivot table
        $departments = Department::all();
        foreach ($departments as $index => $department) {
            // Attach 3-4 random gifts to each department
            $giftIds = collect($createdGifts)->random(rand(3, 4))->pluck('id');
            $department->gifts()->attach($giftIds);
        }

        // Create sample receptionists
        $receptionists = [
            ['full_name' => 'Alice Johnson', 'department_id' => 1],
            ['full_name' => 'Bob Smith', 'department_id' => 2],
            ['full_name' => 'Carol Davis', 'department_id' => 3],
            ['full_name' => 'David Wilson', 'department_id' => 1],
        ];

        foreach ($receptionists as $receptionist) {
            Receptionist::create($receptionist);
        }
    }
}
