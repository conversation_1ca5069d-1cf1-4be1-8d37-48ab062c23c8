<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_counts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('receptionist_id')->constrained()->onDelete('cascade');
            $table->date('date');
            $table->integer('customer_count')->unsigned();
            $table->timestamps();

            // Indexes for performance
            $table->index(['receptionist_id', 'date']);
            $table->index(['date']);

            // Ensure one entry per receptionist per date
            $table->unique(['receptionist_id', 'date'], 'unique_receptionist_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_counts');
    }
};
