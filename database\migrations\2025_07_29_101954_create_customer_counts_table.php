<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_counts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('receptionist_id')->constrained()->onDelete('cascade');
            $table->date('start_date');
            $table->date('end_date');
            $table->integer('customer_count')->unsigned();
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['receptionist_id', 'start_date', 'end_date']);
            $table->index(['start_date', 'end_date']);

            // Ensure no overlapping date ranges for the same receptionist
            $table->unique(['receptionist_id', 'start_date', 'end_date'], 'unique_receptionist_date_range');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_counts');
    }
};
