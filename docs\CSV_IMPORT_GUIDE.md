# Customer Count Excel/CSV Import Guide

## Overview
The Customer Count management system includes an import feature that allows you to bulk import customer count data for multiple receptionists at once using Excel or CSV files with flexible column mapping.

## Supported File Formats
- **Excel files**: .xlsx, .xls
- **CSV files**: .csv

## How to Access
1. Navigate to **Management → Customer Counts** in your admin panel
2. Click the **"Import Excel/CSV"** button in the table header
3. A dialog will open with the import form

## Import Form Fields

### 1. Start Date
- **Purpose**: The beginning date of the customer count period
- **Default**: First day of the current month
- **Required**: Yes

### 2. End Date
- **Purpose**: The ending date of the customer count period  
- **Default**: Today's date
- **Required**: Yes

### 3. Excel/CSV File
- **Purpose**: Upload your Excel or CSV file containing customer count data
- **Accepted formats**: Excel (.xlsx, .xls) and CSV (.csv) files
- **Maximum size**: 5MB
- **Required**: Yes

### 4. Column Mapping (appears after file upload)
After uploading your file, the system will automatically detect the columns and ask you to map them:

#### Receptionist Name Column
- **Purpose**: Select which column contains the receptionist full names
- **Required**: Yes
- **Note**: Names must match existing receptionists in the system

#### Customer Count Column
- **Purpose**: Select which column contains the customer count numbers
- **Required**: Yes
- **Note**: Values must be numeric

## File Format

Your CSV file must contain at least these two types of data:

| Data Type | Description | Example Values |
|-----------|-------------|----------------|
| **Receptionist Names** | Full names of receptionists (must match existing receptionists) | TIMOUMI SOUKAINA, EL MOHTADI FADWA |
| **Customer Counts** | Total customer count for the period (numeric values) | 150, 200, 175 |

### Sample File Content:
**CSV Format:**
```csv
NOM & PRENOM,Total général
TIMOUMI SOUKAINA,150
EL MOHTADI FADWA,200
BOUFRANE ADNANE,175
```

**Excel Format:**
```
| A              | B             |
|----------------|---------------|
| NOM & PRENOM   | Total général |
| TIMOUMI SOUKAINA | 150         |
| EL MOHTADI FADWA | 200         |
```

### Flexible Column Names
The system supports any column names! After upload, you'll select which columns contain:
- **Receptionist names** (could be "Name", "Full Name", "NOM & PRENOM", etc.)
- **Customer counts** (could be "Total", "Count", "Total général", etc.)

## Template Download

In the import dialog, you can download a template file to get started:

- **Download Template**: Gets a CSV file with sample data and correct format containing actual receptionist names from your system.

## How to Import

1. **Fill in the date range**: Set your start and end dates for the customer count period
2. **Upload your file**: Click "Choose file" and select your Excel or CSV file
3. **Map columns**: After upload, select which columns contain receptionist names and customer counts
4. **Download template** (optional): If you need a reference, download the template file first
5. **Click Import**: The system will process your file and show results

## Dynamic Column Detection
The system automatically detects all columns in your file and lets you choose:
- **No fixed column names required** - use any column names you want
- **Flexible mapping** - select the appropriate columns from dropdowns
- **Real-time preview** - see available columns immediately after file upload

## Import Process

1. **File Upload**: Upload your Excel or CSV file
2. **Column Detection**: System automatically detects all available columns
3. **Column Mapping**: You select which columns contain receptionist names and customer counts
4. **Data Processing**: Each row is processed to:
   - Find the matching receptionist by name
   - Validate the customer count is numeric
   - Check for existing records in the same date range
5. **Record Creation/Update**:
   - If a record exists for the same receptionist and date range, it will be updated
   - If no record exists, a new one will be created
6. **Results**: You'll receive notifications about:
   - Number of successfully imported records
   - Any errors encountered (e.g., receptionist not found)

## Error Handling

### Common Errors:
- **"Receptionist not found"**: The name in your file doesn't match any existing receptionist
- **"Selected columns not found"**: The columns you selected are not available in the uploaded file
- **"Please select both columns"**: You must map both receptionist name and customer count columns
- **"Invalid customer count"**: The count value is not a valid number

### Tips for Success:
- Ensure receptionist names in your CSV exactly match those in the system
- Use the "Download Template" button to get a properly formatted sample file
- Check that all customer counts are valid numbers
- Avoid special characters in names that might cause matching issues

## Download Template
Click the **"Download Template"** button in the import dialog to get a sample CSV file with the correct format and some example data.

## Notes
- The import will automatically add "Imported from CSV" as a note for all imported records
- Duplicate records (same receptionist, same date range) will be updated rather than creating duplicates
- The system performs fuzzy matching on receptionist names using LIKE queries
