<x-filament-panels::page>
    <div class="space-y-6">
        {{-- Global Filters --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    🔍 Report Filters
                </h3>
                <div class="flex gap-2">
                    <x-filament::button
                        wire:click="applyFilters"
                        size="sm"
                        color="primary"
                    >
                        Apply Filters
                    </x-filament::button>
                    <x-filament::button
                        wire:click="resetFilters"
                        size="sm"
                        color="gray"
                        outlined
                    >
                        Reset
                    </x-filament::button>
                </div>
            </div>

            <form wire:submit="applyFilters">
                {{ $this->form }}
            </form>
        </div>

        {{-- Filter Summary --}}
        @if($this->getFilteredData()['department_id'] || $this->getFilteredData()['receptionist_id'] || $this->getFilteredData()['start_date'] || $this->getFilteredData()['end_date'])
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 text-sm">
                        <span class="font-medium text-blue-900 dark:text-blue-100">Active Filters:</span>

                        @if($this->getFilteredData()['department_id'])
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                Department: {{ \App\Models\Department::find($this->getFilteredData()['department_id'])?->name }}
                            </span>
                        @endif

                        @if($this->getFilteredData()['receptionist_id'])
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                                Receptionist: {{ \App\Models\Receptionist::find($this->getFilteredData()['receptionist_id'])?->full_name }}
                            </span>
                        @endif

                        @if($this->getFilteredData()['start_date'])
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100">
                                From: {{ \Carbon\Carbon::parse($this->getFilteredData()['start_date'])->format('d/m/Y') }}
                            </span>
                        @endif

                        @if($this->getFilteredData()['end_date'])
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100">
                                To: {{ \Carbon\Carbon::parse($this->getFilteredData()['end_date'])->format('d/m/Y') }}
                            </span>
                        @endif
                    </div>
                </div>
            </div>
        @endif

        {{-- Stats Overview --}}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            {{-- Total Visitors --}}
            <div class="fi-wi-stats-overview-stat relative rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="grid gap-y-2">
                    <div class="flex items-center gap-x-2">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Total Visitors
                        </span>
                    </div>
                    <div class="text-3xl font-semibold tracking-tight text-gray-950 dark:text-white">
                        {{ number_format($this->getTotalVisitors()) }}
                    </div>
                    <div class="flex items-center gap-x-1">
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            Filtered results
                        </span>
                        <x-heroicon-m-arrow-trending-up class="h-4 w-4 text-custom-600 dark:text-custom-400" style="--c-400:var(--success-400);--c-600:var(--success-600);" />
                    </div>
                </div>
            </div>

            {{-- Total Clicks --}}
            <div class="fi-wi-stats-overview-stat relative rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="grid gap-y-2">
                    <div class="flex items-center gap-x-2">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Total Clicks
                        </span>
                    </div>
                    <div class="text-3xl font-semibold tracking-tight text-gray-950 dark:text-white">
                        {{ number_format($this->getTotalClicks()) }}
                    </div>
                    <div class="flex items-center gap-x-1">
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            Platform interactions
                        </span>
                        <x-heroicon-m-cursor-arrow-rays class="h-4 w-4 text-custom-600 dark:text-custom-400" style="--c-400:var(--info-400);--c-600:var(--info-600);" />
                    </div>
                </div>
            </div>

            {{-- Top Receptionist --}}
            <div class="fi-wi-stats-overview-stat relative rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="grid gap-y-2">
                    <div class="flex items-center gap-x-2">
                        <span class="text-sm font-medium text-gray-500 dark:text-gray-400">
                            Top Performer
                        </span>
                    </div>
                    @php $topReceptionist = $this->getTopReceptionist(); @endphp
                    @if($topReceptionist)
                        <div class="text-2xl font-semibold tracking-tight text-gray-950 dark:text-white truncate">
                            {{ $topReceptionist['name'] }}
                        </div>
                        <div class="flex items-center gap-x-1">
                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                {{ $topReceptionist['visits'] }} visits • {{ $topReceptionist['clicks'] }} clicks • {{ $topReceptionist['conversion_rate'] }}%
                            </span>
                            <x-heroicon-m-trophy class="h-4 w-4 text-custom-600 dark:text-custom-400" style="--c-400:var(--warning-400);--c-600:var(--warning-600);" />
                        </div>
                    @else
                        <div class="text-2xl font-semibold tracking-tight text-gray-500 dark:text-gray-400">
                            No data
                        </div>
                        <div class="flex items-center gap-x-1">
                            <span class="text-sm text-gray-500 dark:text-gray-400">
                                Adjust filters to see results
                            </span>
                            <x-heroicon-m-exclamation-triangle class="h-4 w-4 text-gray-400" />
                        </div>
                    @endif
                </div>
            </div>
        </div>

        {{-- Receptionist Performance Table --}}
        <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    📊 Receptionist Performance
                </h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Detailed performance metrics for each receptionist based on applied filters
                </p>
            </div>
            <div class="p-6">
                {{ $this->table }}
            </div>
        </div>

        {{-- Platform Clicks Table --}}
        <div class="fi-ta-ctn divide-y divide-gray-200 overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:divide-white/10 dark:bg-gray-900 dark:ring-white/10"
             x-data="{ refreshing: false }"
             @refresh-platform-clicks.window="refreshing = true; $wire.$refresh().then(() => refreshing = false)">
            <div class="fi-ta-header-ctn divide-y divide-gray-200 dark:divide-white/10">
                <div class="fi-ta-header-toolbar flex items-center justify-between gap-x-4 px-4 py-3 sm:px-6">
                    <div>
                        <h3 class="fi-ta-header-heading text-base font-semibold leading-6 text-gray-950 dark:text-white">
                            🎯 Platform Clicks
                        </h3>
                        <p class="fi-ta-header-description text-sm text-gray-500 dark:text-gray-400">
                            Platform click breakdown by receptionist based on applied filters
                        </p>
                    </div>
                    <div x-show="refreshing" class="flex items-center space-x-2">
                        <svg class="animate-spin h-4 w-4 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span class="text-sm text-primary-500">Updating...</span>
                    </div>
                </div>
            </div>
            <div class="fi-ta-content relative divide-y divide-gray-200 overflow-x-auto dark:divide-white/10 dark:border-t-white/10">
                @php
                    $platformClicksData = $this->getPlatformClicksTableData();
                @endphp

                @if(count($platformClicksData) > 0)
                    <table class="fi-ta-table w-full table-auto divide-y divide-gray-200 text-start dark:divide-white/5">
                        <thead class="divide-y divide-gray-200 dark:divide-white/5">
                            <tr class="bg-gray-50 dark:bg-white/5">
                                <th class="fi-ta-header-cell px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6">
                                    <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                        <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">
                                            Receptionist
                                        </span>
                                    </span>
                                </th>
                                <th class="fi-ta-header-cell px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6">
                                    <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                        <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">
                                            Department
                                        </span>
                                    </span>
                                </th>
                                <th class="fi-ta-header-cell px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6">
                                    <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-start">
                                        <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">
                                            Platform
                                        </span>
                                    </span>
                                </th>
                                <th class="fi-ta-header-cell px-3 py-3.5 sm:first-of-type:ps-6 sm:last-of-type:pe-6">
                                    <span class="group flex w-full items-center gap-x-1 whitespace-nowrap justify-center">
                                        <span class="fi-ta-header-cell-label text-sm font-semibold text-gray-950 dark:text-white">
                                            Clicks
                                        </span>
                                    </span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 whitespace-nowrap dark:divide-white/5">
                                @foreach($platformClicksData as $row)
                                    <tr class="fi-ta-row [@media(hover:hover)]:transition [@media(hover:hover)]:duration-75 hover:bg-gray-50 dark:hover:bg-white/5 {{ $row['is_first_row'] ? 'border-t-2 border-primary-200 dark:border-primary-700' : '' }}">
                                        <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                            <div class="fi-ta-col-wrp">
                                                <div class="fi-ta-text grid w-full gap-y-1 px-3 py-4">
                                                    <div class="flex">
                                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5 text-sm leading-6 text-gray-950 dark:text-white {{ $row['is_first_row'] ? 'font-medium' : '' }}">
                                                            {{ $row['receptionist_name'] }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                            <div class="fi-ta-col-wrp">
                                                <div class="fi-ta-text grid w-full gap-y-1 px-3 py-4">
                                                    <div class="flex">
                                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5 text-sm leading-6 text-gray-500 dark:text-gray-400">
                                                            {{ $row['receptionist_department'] }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                            <div class="fi-ta-col-wrp">
                                                <div class="fi-ta-text grid w-full gap-y-1 px-3 py-4">
                                                    <div class="flex">
                                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5">
                                                            <span class="fi-badge flex items-center justify-center gap-x-1 rounded-md text-xs font-medium ring-1 ring-inset px-2 min-w-[theme(spacing.6)] py-1 fi-color-custom bg-custom-50 text-custom-600 ring-custom-600/10 dark:bg-custom-400/10 dark:text-custom-400 dark:ring-custom-400/30" style="--c-50:var(--primary-50);--c-400:var(--primary-400);--c-600:var(--primary-600);">
                                                                {{ $row['platform_name'] }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="fi-ta-cell p-0 first-of-type:ps-1 last-of-type:pe-1 sm:first-of-type:ps-3 sm:last-of-type:pe-3">
                                            <div class="fi-ta-col-wrp">
                                                <div class="fi-ta-text grid w-full gap-y-1 px-3 py-4">
                                                    <div class="flex justify-center">
                                                        <div class="fi-ta-text-item inline-flex items-center gap-1.5">
                                                            <span class="fi-badge flex items-center justify-center gap-x-1 rounded-md text-xs font-medium ring-1 ring-inset px-2 min-w-[theme(spacing.6)] py-1 {{ $row['clicks_count'] > 0 ? 'fi-color-success bg-success-50 text-success-600 ring-success-600/10 dark:bg-success-400/10 dark:text-success-400 dark:ring-success-400/30' : 'fi-color-gray bg-gray-50 text-gray-600 ring-gray-600/10 dark:bg-gray-400/10 dark:text-gray-400 dark:ring-gray-400/30' }}">
                                                                {{ number_format($row['clicks_count']) }}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                @else
                    <div class="fi-ta-empty-state px-6 py-12">
                        <div class="fi-ta-empty-state-content mx-auto grid max-w-lg justify-items-center text-center">
                            <div class="fi-ta-empty-state-icon-ctn mb-4 rounded-full bg-gray-100 p-3 dark:bg-gray-500/20">
                                <svg class="fi-ta-empty-state-icon h-6 w-6 text-gray-500 dark:text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
                                </svg>
                            </div>
                            <h4 class="fi-ta-empty-state-heading text-base font-semibold leading-6 text-gray-950 dark:text-white">
                                No platform clicks found
                            </h4>
                            <p class="fi-ta-empty-state-description text-sm text-gray-500 dark:text-gray-400">
                                No platform clicks found for the selected filters
                            </p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-filament-panels::page>
