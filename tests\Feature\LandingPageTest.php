<?php

namespace Tests\Feature;

use App\Models\Department;
use App\Models\Receptionist;
use App\Models\Platform;
use App\Models\Gift;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class LandingPageTest extends TestCase
{
    use RefreshDatabase;

    public function test_landing_page_displays_correctly(): void
    {
        // Create test data
        $department = Department::factory()->create(['name' => 'Test Department']);
        $platform = Platform::factory()->create(['name' => 'Test Platform']);
        $gift = Gift::factory()->create(['department_id' => $department->id]);
        $receptionist = Receptionist::factory()->create([
            'department_id' => $department->id,
            'full_name' => 'Test Receptionist'
        ]);

        // Visit landing page
        $response = $this->get("/landing/{$receptionist->unique_url}");

        $response->assertStatus(200);
        $response->assertSee($receptionist->full_name);
        $response->assertSee($department->name);
        $response->assertSee($platform->name);
    }

    public function test_invalid_landing_page_returns_404(): void
    {
        $response = $this->get('/landing/invalid-url');
        $response->assertStatus(404);
    }

    public function test_visit_tracking_works(): void
    {
        $department = Department::factory()->create();
        $receptionist = Receptionist::factory()->create(['department_id' => $department->id]);

        $this->assertDatabaseCount('visits', 0);

        $this->get("/landing/{$receptionist->unique_url}");

        $this->assertDatabaseCount('visits', 1);
        $this->assertDatabaseHas('visits', [
            'receptionist_id' => $receptionist->id
        ]);
    }
}
