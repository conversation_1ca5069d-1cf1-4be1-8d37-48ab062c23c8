<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gifts', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['department_id']);

            // Make department_id nullable
            $table->foreignId('department_id')->nullable()->change();

            // Re-add the foreign key constraint with nullable support
            $table->foreign('department_id')->references('id')->on('departments')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gifts', function (Blueprint $table) {
            // Drop the nullable foreign key
            $table->dropForeign(['department_id']);

            // Make department_id required again
            $table->foreignId('department_id')->change();

            // Re-add the foreign key constraint with cascade delete
            $table->foreign('department_id')->references('id')->on('departments')->onDelete('cascade');
        });
    }
};
