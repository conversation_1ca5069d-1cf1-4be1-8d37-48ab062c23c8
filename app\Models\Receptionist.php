<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Receptionist extends Model
{
    use HasFactory;

    protected $fillable = [
        'department_id',
        'full_name',
        'unique_url',
        'qr_code_path',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($receptionist) {
            if (empty($receptionist->unique_url)) {
                $receptionist->unique_url = Str::random(8);
            }
        });
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function getLandingUrlAttribute(): string
    {
        return url("/landing/{$this->unique_url}");
    }

    public function visits(): HasMany
    {
        return $this->hasMany(Visit::class);
    }

    public function clicks(): HasMany
    {
        return $this->hasMany(Click::class);
    }

    public function customerCounts(): HasMany
    {
        return $this->hasMany(CustomerCount::class);
    }

    /**
     * Calculate conversion rate for a specific date range.
     * Conversion rate = (Total Visitors / Total Customers) * 100
     */
    public function getConversionRate(Carbon $startDate, Carbon $endDate): float
    {
        // Get total customers for the date range
        $totalCustomers = $this->customerCounts()
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->sum('customer_count');

        if ($totalCustomers == 0) {
            return 0.0;
        }

        // Get total visitors for the date range
        $totalVisitors = $this->visits()
            ->whereBetween('visited_at', [$startDate, $endDate])
            ->count();

        return round(($totalVisitors / $totalCustomers) * 100, 2);
    }

    /**
     * Get conversion rate for the current month.
     */
    public function getCurrentMonthConversionRate(): float
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();

        return $this->getConversionRate($startOfMonth, $endOfMonth);
    }

    /**
     * Get total customers for a specific date range.
     */
    public function getTotalCustomers(Carbon $startDate, Carbon $endDate): int
    {
        return $this->customerCounts()
            ->whereBetween('date', [$startDate->format('Y-m-d'), $endDate->format('Y-m-d')])
            ->sum('customer_count');
    }

    /**
     * Get total clicks for a specific date range.
     */
    public function getTotalClicks(Carbon $startDate, Carbon $endDate): int
    {
        return $this->clicks()
            ->whereBetween('clicked_at', [$startDate, $endDate])
            ->count();
    }

    /**
     * Get total visits for a specific date range.
     */
    public function getTotalVisits(Carbon $startDate, Carbon $endDate): int
    {
        return $this->visits()
            ->whereBetween('visited_at', [$startDate, $endDate])
            ->count();
    }
}
