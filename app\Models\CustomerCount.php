<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CustomerCount extends Model
{
    use HasFactory;

    protected $fillable = [
        'receptionist_id',
        'start_date',
        'end_date',
        'customer_count',
        'notes',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'customer_count' => 'integer',
    ];

    /**
     * Get the receptionist that owns the customer count.
     */
    public function receptionist(): BelongsTo
    {
        return $this->belongsTo(Receptionist::class);
    }

    /**
     * Get the number of days in this date range.
     */
    public function getDaysCountAttribute(): int
    {
        if (!$this->start_date || !$this->end_date) {
            return 1; // Default to 1 day if dates are missing
        }

        $diff = $this->start_date->diffInDays($this->end_date);
        return $diff + 1; // Add 1 because both start and end dates are inclusive
    }

    /**
     * Get the average customers per day.
     */
    public function getAverageCustomersPerDayAttribute(): float
    {
        $daysCount = $this->days_count;
        if ($daysCount <= 0) {
            return 0.0;
        }
        return round($this->customer_count / $daysCount, 2);
    }

    /**
     * Get the date range as a formatted string.
     */
    public function getDateRangeAttribute(): string
    {
        if ($this->start_date->isSameDay($this->end_date)) {
            return $this->start_date->format('M j, Y');
        }
        
        return $this->start_date->format('M j') . ' - ' . $this->end_date->format('M j, Y');
    }

    /**
     * Scope to filter by date range overlap.
     */
    public function scopeOverlappingDateRange($query, Carbon $startDate, Carbon $endDate, ?int $excludeId = null)
    {
        $query->where(function ($q) use ($startDate, $endDate) {
            $q->whereBetween('start_date', [$startDate, $endDate])
              ->orWhereBetween('end_date', [$startDate, $endDate])
              ->orWhere(function ($subQ) use ($startDate, $endDate) {
                  $subQ->where('start_date', '<=', $startDate)
                       ->where('end_date', '>=', $endDate);
              });
        });

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query;
    }

    /**
     * Scope to filter by receptionist.
     */
    public function scopeForReceptionist($query, int $receptionistId)
    {
        return $query->where('receptionist_id', $receptionistId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeInDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->where('start_date', '>=', $startDate)
                    ->where('end_date', '<=', $endDate);
    }
}
