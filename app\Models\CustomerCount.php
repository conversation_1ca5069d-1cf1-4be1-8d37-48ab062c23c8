<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CustomerCount extends Model
{
    use HasFactory;

    protected $fillable = [
        'receptionist_id',
        'date',
        'customer_count',
    ];

    protected $casts = [
        'date' => 'date',
        'customer_count' => 'integer',
    ];

    /**
     * Get the receptionist that owns the customer count.
     */
    public function receptionist(): BelongsTo
    {
        return $this->belongsTo(Receptionist::class);
    }



    /**
     * Scope to filter by receptionist.
     */
    public function scopeForReceptionist($query, int $receptionistId)
    {
        return $query->where('receptionist_id', $receptionistId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeInDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->where('date', '>=', $startDate)
                    ->where('date', '<=', $endDate);
    }

    /**
     * Scope to filter by specific date.
     */
    public function scopeForDate($query, Carbon $date)
    {
        return $query->where('date', $date->format('Y-m-d'));
    }
}
