<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;

class CustomerCount extends Model
{
    use HasFactory;

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Add validation before saving to ensure uniqueness
        static::saving(function ($customerCount) {
            $query = static::where('receptionist_id', $customerCount->receptionist_id)
                          ->where('date', $customerCount->date);

            // Exclude current record if updating
            if ($customerCount->exists) {
                $query->where('id', '!=', $customerCount->id);
            }

            if ($query->exists()) {
                throw ValidationException::withMessages([
                    'date' => 'A customer count record already exists for this receptionist on this date.'
                ]);
            }
        });
    }

    protected $fillable = [
        'receptionist_id',
        'date',
        'customer_count',
    ];

    protected $casts = [
        'date' => 'date',
        'customer_count' => 'integer',
    ];

    /**
     * Get the receptionist that owns the customer count.
     */
    public function receptionist(): BelongsTo
    {
        return $this->belongsTo(Receptionist::class);
    }



    /**
     * Scope to filter by receptionist.
     */
    public function scopeForReceptionist($query, int $receptionistId)
    {
        return $query->where('receptionist_id', $receptionistId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeInDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->where('date', '>=', $startDate)
                    ->where('date', '<=', $endDate);
    }

    /**
     * Scope to filter by specific date.
     */
    public function scopeForDate($query, Carbon $date)
    {
        return $query->where('date', $date->format('Y-m-d'));
    }

    /**
     * Find or create a customer count record for a specific receptionist and date.
     */
    public static function findOrCreateForReceptionistAndDate(int $receptionistId, Carbon $date, int $customerCount = 0): self
    {
        return static::firstOrCreate(
            [
                'receptionist_id' => $receptionistId,
                'date' => $date->format('Y-m-d'),
            ],
            [
                'customer_count' => $customerCount,
            ]
        );
    }

    /**
     * Update or create a customer count record for a specific receptionist and date.
     */
    public static function updateOrCreateForReceptionistAndDate(int $receptionistId, Carbon $date, int $customerCount): self
    {
        return static::updateOrCreate(
            [
                'receptionist_id' => $receptionistId,
                'date' => $date->format('Y-m-d'),
            ],
            [
                'customer_count' => $customerCount,
            ]
        );
    }
}
