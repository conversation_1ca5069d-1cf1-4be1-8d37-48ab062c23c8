<?php

namespace App\Filament\Pages;

use App\Models\Setting;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Exceptions\Halt;
use Illuminate\Support\Facades\Storage;

class Settings extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $view = 'filament.pages.settings';

    protected static ?string $title = 'System Settings';

    protected static ?string $navigationLabel = 'Settings';

    protected static ?int $navigationSort = 99;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill([
            'brand_name' => Setting::get('brand_name', 'Review Tracker'),
            'brand_logo' => Setting::get('brand_logo'),
            'dark_brand_logo' => Setting::get('dark_brand_logo'),
            'favicon' => Setting::get('favicon'),
            'primary_color' => Setting::get('primary_color', '#92541B'),
            'navigation_type' => Setting::get('navigation_type', 'sidebar'),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Brand Settings')
                    ->description('Configure your application branding and appearance')
                    ->schema([
                        Forms\Components\TextInput::make('brand_name')
                            ->label('Brand Name')
                            ->required()
                            ->maxLength(255)
                            ->helperText('The name displayed in the admin panel header'),

                        Forms\Components\FileUpload::make('brand_logo')
                            ->label('Brand Logo')
                            ->image()
                            ->disk('public')
                            ->directory('assets/logos')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/svg+xml', 'image/png', 'image/jpeg'])
                            ->maxSize(2048)
                            ->helperText('Upload SVG, PNG, or JPG. Recommended size: 120x40px'),

                        Forms\Components\FileUpload::make('dark_brand_logo')
                            ->label('Dark Mode Brand Logo')
                            ->image()
                            ->disk('public')
                            ->directory('assets/logos')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/svg+xml', 'image/png', 'image/jpeg'])
                            ->maxSize(2048)
                            ->helperText('Logo for dark mode. Same format as brand logo'),

                        Forms\Components\FileUpload::make('favicon')
                            ->label('Favicon')
                            ->image()
                            ->disk('public')
                            ->directory('assets/icons')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/x-icon', 'image/png', 'image/svg+xml'])
                            ->maxSize(1024)
                            ->helperText('Upload ICO, PNG, or SVG. Recommended size: 32x32px'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Theme Settings')
                    ->description('Customize the visual appearance of your admin panel')
                    ->schema([
                        Forms\Components\ColorPicker::make('primary_color')
                            ->label('Primary Color')
                            ->required()
                            ->helperText('The main color used throughout the admin interface'),

                        Forms\Components\Select::make('navigation_type')
                            ->label('Navigation Type')
                            ->required()
                            ->options([
                                'sidebar' => 'Sidebar Navigation',
                                'top' => 'Top Navigation',
                            ])
                            ->helperText('Choose between sidebar or top navigation layout'),
                    ])
                    ->columns(2),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            \Filament\Actions\Action::make('save')
                ->label('Save Settings')
                ->action('save')
                ->color('primary'),
        ];
    }

    public function save(): void
    {
        try {
            $data = $this->form->getState();

            // Save brand settings
            Setting::set('brand_name', $data['brand_name'], 'string', 'Application brand name');

            // Handle file uploads
            if (!empty($data['brand_logo'])) {
                $logoPath = is_array($data['brand_logo']) ? $data['brand_logo'][0] : $data['brand_logo'];
                Setting::set('brand_logo', $logoPath, 'file', 'Brand logo file path');
            }

            if (!empty($data['dark_brand_logo'])) {
                $darkLogoPath = is_array($data['dark_brand_logo']) ? $data['dark_brand_logo'][0] : $data['dark_brand_logo'];
                Setting::set('dark_brand_logo', $darkLogoPath, 'file', 'Dark mode brand logo file path');
            }

            if (!empty($data['favicon'])) {
                $faviconPath = is_array($data['favicon']) ? $data['favicon'][0] : $data['favicon'];
                Setting::set('favicon', $faviconPath, 'file', 'Favicon file path');
            }

            // Save theme settings
            Setting::set('primary_color', $data['primary_color'], 'string', 'Primary theme color');
            Setting::set('navigation_type', $data['navigation_type'], 'string', 'Navigation layout type');

            // Clear settings cache
            Setting::clearCache();

            // Clear config cache to refresh panel settings
            \Illuminate\Support\Facades\Artisan::call('config:clear');

            Notification::make()
                ->title('Settings saved successfully!')
                ->body('Page will refresh automatically to apply changes.')
                ->success()
                ->send();

            // Force page refresh to apply panel changes
            $this->js('setTimeout(() => window.location.reload(), 1500);');

        } catch (Halt $exception) {
            return;
        }
    }
}
