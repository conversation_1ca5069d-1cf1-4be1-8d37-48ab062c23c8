<?php

namespace App\Filament\Widgets;

use App\Models\Visit;
use App\Models\Click;
use App\Models\Receptionist;
use App\Models\Department;
use App\Models\CustomerCount;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class AnalyticsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $thisMonth = Carbon::now()->startOfMonth();

        // Visitors stats
        $visitorsToday = Visit::whereDate('visited_at', $today)->count();
        $visitorsYesterday = Visit::whereDate('visited_at', $yesterday)->count();
        $visitorsThisMonth = Visit::where('visited_at', '>=', $thisMonth)->count();

        // Clicks stats
        $clicksToday = Click::whereDate('clicked_at', $today)->count();
        $clicksYesterday = Click::whereDate('clicked_at', $yesterday)->count();
        $clicksThisMonth = Click::where('clicked_at', '>=', $thisMonth)->count();

        // Top receptionist by clicks this month
        $topReceptionist = Receptionist::withCount(['clicks' => function ($query) use ($thisMonth) {
            $query->where('clicked_at', '>=', $thisMonth);
        }])
        ->orderBy('clicks_count', 'desc')
        ->first();

        // Top department by visitors this month
        $topDepartment = Department::select('departments.*')
            ->selectRaw('COUNT(visits.id) as total_visits')
            ->leftJoin('receptionists', 'departments.id', '=', 'receptionists.department_id')
            ->leftJoin('visits', 'receptionists.id', '=', 'visits.receptionist_id')
            ->where('visits.visited_at', '>=', $thisMonth)
            ->groupBy('departments.id', 'departments.name', 'departments.is_active', 'departments.created_at', 'departments.updated_at')
            ->orderBy('total_visits', 'desc')
            ->first();

        // Calculate overall conversion rate for this month
        $endOfMonth = Carbon::now()->endOfMonth();
        $totalCustomersThisMonth = CustomerCount::whereBetween('date', [$thisMonth->format('Y-m-d'), $endOfMonth->format('Y-m-d')])
            ->sum('customer_count');

        $overallConversionRate = $totalCustomersThisMonth > 0
            ? round(($visitorsThisMonth / $totalCustomersThisMonth) * 100, 2)
            : 0;

        // Top receptionist by conversion rate this month
        $topConversionReceptionist = Receptionist::with('customerCounts')
            ->get()
            ->map(function ($receptionist) use ($thisMonth) {
                $endOfMonth = Carbon::now()->endOfMonth();
                $conversionRate = $receptionist->getConversionRate($thisMonth, $endOfMonth);
                $totalCustomers = $receptionist->getTotalCustomers($thisMonth, $endOfMonth);

                return [
                    'receptionist' => $receptionist,
                    'conversion_rate' => $conversionRate,
                    'total_customers' => $totalCustomers,
                ];
            })
            ->filter(function ($item) {
                return $item['total_customers'] > 0; // Only include receptionists with customer data
            })
            ->sortByDesc('conversion_rate')
            ->first();

        return [
            Stat::make('Visitors Today', $visitorsToday)
                ->description($visitorsYesterday > 0 ?
                    ($visitorsToday > $visitorsYesterday ?
                        '+' . round((($visitorsToday - $visitorsYesterday) / $visitorsYesterday) * 100, 1) . '% from yesterday' :
                        '-' . round((($visitorsYesterday - $visitorsToday) / $visitorsYesterday) * 100, 1) . '% from yesterday'
                    ) : 'No data from yesterday'
                )
                ->descriptionIcon($visitorsToday > $visitorsYesterday ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($visitorsToday > $visitorsYesterday ? 'success' : 'danger'),

            Stat::make('Clicks Today', $clicksToday)
                ->description($clicksYesterday > 0 ?
                    ($clicksToday > $clicksYesterday ?
                        '+' . round((($clicksToday - $clicksYesterday) / $clicksYesterday) * 100, 1) . '% from yesterday' :
                        '-' . round((($clicksYesterday - $clicksToday) / $clicksYesterday) * 100, 1) . '% from yesterday'
                    ) : 'No data from yesterday'
                )
                ->descriptionIcon($clicksToday > $clicksYesterday ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($clicksToday > $clicksYesterday ? 'success' : 'danger'),

            Stat::make('Visitors This Month', $visitorsThisMonth)
                ->description('Total unique visits')
                ->descriptionIcon('heroicon-m-eye')
                ->color('info'),

            Stat::make('Clicks This Month', $clicksThisMonth)
                ->description('Total platform clicks')
                ->descriptionIcon('heroicon-m-cursor-arrow-rays')
                ->color('warning'),

            Stat::make('Top Receptionist', $topReceptionist ? $topReceptionist->full_name : 'No data')
                ->description($topReceptionist ? $topReceptionist->clicks_count . ' clicks this month' : 'No clicks yet')
                ->descriptionIcon('heroicon-m-trophy')
                ->color('success'),

            Stat::make('Top Department', $topDepartment ? $topDepartment->name : 'No data')
                ->description($topDepartment ? $topDepartment->total_visits . ' visits this month' : 'No visits yet')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('primary'),

            Stat::make('Overall Conversion Rate', $overallConversionRate . '%')
                ->description($totalCustomersThisMonth > 0 ?
                    $visitorsThisMonth . ' visitors from ' . number_format($totalCustomersThisMonth) . ' customers' :
                    'No customer data available'
                )
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color($overallConversionRate >= 10 ? 'success' : ($overallConversionRate >= 5 ? 'warning' : 'danger')),

            Stat::make('Top Conversion Rate',
                $topConversionReceptionist ?
                    $topConversionReceptionist['receptionist']->full_name :
                    'No data'
            )
                ->description($topConversionReceptionist ?
                    $topConversionReceptionist['conversion_rate'] . '% conversion rate' :
                    'No customer data yet'
                )
                ->descriptionIcon('heroicon-m-star')
                ->color('success'),
        ];
    }
}
