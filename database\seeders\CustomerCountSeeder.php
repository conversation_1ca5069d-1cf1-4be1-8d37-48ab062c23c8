<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\CustomerCount;
use App\Models\Receptionist;
use Carbon\Carbon;

class CustomerCountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all active receptionists
        $receptionists = Receptionist::where('is_active', true)->get();

        if ($receptionists->isEmpty()) {
            $this->command->info('No active receptionists found. Please create some receptionists first.');
            return;
        }

        foreach ($receptionists as $receptionist) {
            // Create customer counts for the last 3 months
            for ($i = 2; $i >= 0; $i--) {
                $startDate = Carbon::now()->subMonths($i)->startOfMonth();
                $endDate = Carbon::now()->subMonths($i)->endOfMonth();

                // Skip if this month isn't complete yet (except current month)
                if ($i === 0 || $endDate->isPast()) {
                    CustomerCount::factory()
                        ->forDateRange($startDate, $endDate)
                        ->create([
                            'receptionist_id' => $receptionist->id,
                            'customer_count' => fake()->numberBetween(50, 300),
                            'notes' => $i === 0 ? 'Current month data' : "Data for {$startDate->format('F Y')}",
                        ]);
                }
            }

            // Create some weekly data for the current month
            $currentMonth = Carbon::now()->startOfMonth();
            $weeksInMonth = $currentMonth->diffInWeeks(Carbon::now()->endOfMonth());

            for ($week = 0; $week < min($weeksInMonth, 3); $week++) {
                $weekStart = $currentMonth->copy()->addWeeks($week);
                $weekEnd = $weekStart->copy()->addDays(6)->min(Carbon::now());

                if ($weekEnd->isPast() && $weekStart->month === $currentMonth->month) {
                    CustomerCount::factory()
                        ->forDateRange($weekStart, $weekEnd)
                        ->create([
                            'receptionist_id' => $receptionist->id,
                            'customer_count' => fake()->numberBetween(10, 80),
                            'notes' => "Week {$week + 1} of {$currentMonth->format('F Y')}",
                        ]);
                }
            }
        }

        $this->command->info('Customer count data seeded successfully!');
    }
}
