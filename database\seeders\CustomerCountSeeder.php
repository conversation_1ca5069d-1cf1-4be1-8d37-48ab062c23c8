<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\CustomerCount;
use App\Models\Receptionist;
use Carbon\Carbon;

class CustomerCountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all active receptionists
        $receptionists = Receptionist::where('is_active', true)->get();

        if ($receptionists->isEmpty()) {
            $this->command->info('No active receptionists found. Please create some receptionists first.');
            return;
        }

        foreach ($receptionists as $receptionist) {
            // Create daily customer counts for the last 30 days
            $startDate = Carbon::now()->subDays(30);
            $endDate = Carbon::now()->subDay(); // Don't include today

            $currentDate = $startDate->copy();
            while ($currentDate->lte($endDate)) {
                // Skip weekends for more realistic data (optional)
                if ($currentDate->isWeekday()) {
                    CustomerCount::factory()
                        ->forDate($currentDate->copy())
                        ->create([
                            'receptionist_id' => $receptionist->id,
                            'customer_count' => fake()->numberBetween(5, 50),
                        ]);
                }
                $currentDate->addDay();
            }

            // Create some data for the current week (including today)
            $startOfWeek = Carbon::now()->startOfWeek();
            $currentDate = $startOfWeek->copy();

            while ($currentDate->lte(Carbon::now())) {
                if ($currentDate->isWeekday()) {
                    // Check if record already exists to avoid duplicates
                    $existing = CustomerCount::where('receptionist_id', $receptionist->id)
                        ->where('date', $currentDate->format('Y-m-d'))
                        ->exists();

                    if (!$existing) {
                        CustomerCount::factory()
                            ->forDate($currentDate->copy())
                            ->create([
                                'receptionist_id' => $receptionist->id,
                                'customer_count' => fake()->numberBetween(8, 35),
                            ]);
                    }
                }
                $currentDate->addDay();
            }
        }

        $this->command->info('Customer count data seeded successfully!');
    }
}
