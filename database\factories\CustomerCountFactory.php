<?php

namespace Database\Factories;

use App\Models\CustomerCount;
use App\Models\Receptionist;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerCount>
 */
class CustomerCountFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('-3 months', 'now');
        $endDate = (clone $startDate)->modify('+' . $this->faker->numberBetween(1, 30) . ' days');

        return [
            'receptionist_id' => Receptionist::factory(),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'customer_count' => $this->faker->numberBetween(10, 500),
            'notes' => $this->faker->optional(0.3)->sentence(),
        ];
    }

    /**
     * Create a customer count for the current month.
     */
    public function currentMonth(): static
    {
        return $this->state(function (array $attributes) {
            $startOfMonth = Carbon::now()->startOfMonth();
            $endOfMonth = Carbon::now()->endOfMonth();

            return [
                'start_date' => $startOfMonth,
                'end_date' => $endOfMonth,
                'customer_count' => $this->faker->numberBetween(50, 300),
            ];
        });
    }

    /**
     * Create a customer count for a specific date range.
     */
    public function forDateRange(Carbon $startDate, Carbon $endDate): static
    {
        return $this->state(function (array $attributes) use ($startDate, $endDate) {
            return [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ];
        });
    }

    /**
     * Create a customer count with high customer volume.
     */
    public function highVolume(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'customer_count' => $this->faker->numberBetween(200, 1000),
            ];
        });
    }

    /**
     * Create a customer count with low customer volume.
     */
    public function lowVolume(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'customer_count' => $this->faker->numberBetween(5, 50),
            ];
        });
    }
}
