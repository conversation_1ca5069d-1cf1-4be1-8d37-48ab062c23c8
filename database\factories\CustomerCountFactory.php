<?php

namespace Database\Factories;

use App\Models\CustomerCount;
use App\Models\Receptionist;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerCount>
 */
class CustomerCountFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'receptionist_id' => Receptionist::factory(),
            'date' => $this->faker->dateTimeBetween('-3 months', 'now'),
            'customer_count' => $this->faker->numberBetween(5, 50),
        ];
    }

    /**
     * Create a customer count for today.
     */
    public function today(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'date' => Carbon::today(),
                'customer_count' => $this->faker->numberBetween(10, 50),
            ];
        });
    }

    /**
     * Create a customer count for a specific date.
     */
    public function forDate(Carbon $date): static
    {
        return $this->state(function (array $attributes) use ($date) {
            return [
                'date' => $date,
            ];
        });
    }

    /**
     * Create a customer count with high customer volume.
     */
    public function highVolume(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'customer_count' => $this->faker->numberBetween(200, 1000),
            ];
        });
    }

    /**
     * Create a customer count with low customer volume.
     */
    public function lowVolume(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'customer_count' => $this->faker->numberBetween(5, 50),
            ];
        });
    }
}
