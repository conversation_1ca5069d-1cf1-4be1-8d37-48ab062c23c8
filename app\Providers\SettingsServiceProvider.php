<?php

namespace App\Providers;

use App\Models\Setting;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;

class SettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Only load settings if the settings table exists
        if (Schema::hasTable('settings')) {
            try {
                // Load settings into config
                $settings = Setting::all();
                foreach ($settings as $setting) {
                    config(['settings.' . $setting->key => $setting->value]);
                }
            } catch (\Exception $e) {
                // Silently fail if database is not available
            }
        }
    }
}
