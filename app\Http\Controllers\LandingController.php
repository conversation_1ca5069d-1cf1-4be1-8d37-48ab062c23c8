<?php

namespace App\Http\Controllers;

use App\Models\Receptionist;
use App\Models\Platform;
use App\Models\Visit;
use App\Models\Click;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class LandingController extends Controller
{
    public function show(string $unique_url, Request $request)
    {
        $receptionist = Receptionist::where('unique_url', $unique_url)
            ->where('is_active', true)
            ->with(['department.gifts' => function ($query) {
                $query->where('is_active', true);
            }])
            ->firstOrFail();

        // Debug: Log the gifts data
        \Log::info('Landing page accessed', [
            'receptionist' => $receptionist->full_name,
            'department' => $receptionist->department->name,
            'gifts_count' => $receptionist->department->gifts->count(),
            'gifts' => $receptionist->department->gifts->pluck('name')->toArray()
        ]);

        // Get or create visitor UUID from cookie
        $visitorUuid = $request->cookie('visitor_uuid');
        if (!$visitorUuid) {
            $visitorUuid = Str::uuid()->toString();
        }

        // Create cookie name specific to this receptionist to track visits per receptionist
        $visitCookieName = "visited_receptionist_{$receptionist->id}";

        // Check if this visitor has already visited this receptionist
        $hasVisited = $request->cookie($visitCookieName);

        if (!$hasVisited) {
            // Track visit only if not visited before
            Visit::create([
                'receptionist_id' => $receptionist->id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'referrer' => $request->header('referer'),
                'visited_at' => now(),
            ]);
        }

        // Get active platforms
        $platforms = Platform::where('is_active', true)->get();

        $response = response()->view('landing.show', compact('receptionist', 'platforms'));

        // Set cookies (expire in 1 year)
        $response->cookie('visitor_uuid', $visitorUuid, 525600); // 1 year in minutes
        $response->cookie($visitCookieName, 'true', 525600); // 1 year in minutes

        return $response;
    }

    public function trackClick(Request $request, string $unique_url)
    {
        $receptionist = Receptionist::where('unique_url', $unique_url)
            ->where('is_active', true)
            ->firstOrFail();

        $platform = Platform::findOrFail($request->platform_id);

        // Create cookie name specific to this receptionist and platform combination
        $clickCookieName = "clicked_receptionist_{$receptionist->id}_platform_{$platform->id}";

        // Check if this visitor has already clicked this platform for this receptionist
        $hasClicked = $request->cookie($clickCookieName);

        if (!$hasClicked) {
            // Track click only if not clicked before
            Click::create([
                'receptionist_id' => $receptionist->id,
                'platform_id' => $platform->id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'clicked_at' => now(),
            ]);
        }

        // Return JSON response with platform URL and cookie setting for JavaScript to handle
        $response = response()->json([
            'success' => true,
            'platform_url' => $platform->url,
            'platform_name' => $platform->name,
            'already_clicked' => (bool) $hasClicked
        ]);

        // Set cookie to prevent duplicate clicks (expire in 1 year)
        $response->cookie($clickCookieName, 'true', 525600); // 1 year in minutes

        return $response;
    }
}
