<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomerCountResource\Pages;
use App\Models\CustomerCount;
use App\Models\Receptionist;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;

class CustomerCountResource extends Resource
{
    protected static ?string $model = CustomerCount::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Management';

    protected static ?string $navigationLabel = 'Customer Counts';

    protected static ?string $modelLabel = 'Customer Count';

    protected static ?string $pluralModelLabel = 'Customer Counts'; 

    protected static ?int $navigationSort = 5;



    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('receptionist.full_name')
                    ->label('Receptionist')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('receptionist.department.name')
                    ->label('Department')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('date')
                    ->label('Date')
                    ->date()
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('customer_count')
                    ->label('Customers')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Add Customer Count')
                    ->icon('heroicon-o-plus')
                    ->form([
                        Forms\Components\Select::make('receptionist_id')
                            ->relationship('receptionist', 'full_name')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                // Clear date when receptionist changes to avoid conflicts
                                $set('date', null);
                            }),

                        Forms\Components\DatePicker::make('date')
                            ->required()
                            ->default(Carbon::today())
                            ->live()
                            ->rules([
                                function (Forms\Get $get) {
                                    return function (string $attribute, $value, \Closure $fail) use ($get) {
                                        if (!$value || !$get('receptionist_id')) return;

                                        $date = Carbon::parse($value);
                                        $receptionistId = $get('receptionist_id');

                                        // Check for existing record on the same date
                                        $existing = CustomerCount::where('receptionist_id', $receptionistId)
                                            ->where('date', $date->format('Y-m-d'))
                                            ->exists();

                                        if ($existing) {
                                            $fail('A customer count record already exists for this receptionist on this date.');
                                        }
                                    };
                                },
                            ]),

                        Forms\Components\TextInput::make('customer_count')
                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->step(1)
                            ->helperText('Total number of customers served on this date'),
                    ]),

                Action::make('import')
                    ->label('Import Excel/CSV')
                    ->icon('heroicon-o-arrow-up-tray')
                    ->color('success')
                    ->form([
                        FileUpload::make('import_file')
                            ->label('Excel/CSV File')
                            ->required()
                            ->disk('local')
                            ->directory('imports')
                            ->acceptedFileTypes([
                                'text/csv',
                                'application/csv',
                                'text/plain',
                                'application/vnd.ms-excel',
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                'application/excel'
                            ])
                            ->maxSize(5120) // 5MB
                            ->helperText('Upload Excel (.xlsx, .xls) or CSV file. After upload, you will select which columns contain receptionist names and customer counts.')
->afterStateUpdated(function ($state, $set, $get) {
                                // Only process when file is actually uploaded
                                if (!$state || !is_object($state)) {
                                    return;
                                }

                                // Add a small delay to ensure file is fully uploaded
                                usleep(100000); // 0.1 second

                                try {
                                    $filePath = null;

                                    // Try different methods to get the file path
                                    if (method_exists($state, 'getRealPath')) {
                                        $filePath = $state->getRealPath();
                                    } elseif (method_exists($state, 'getPathname')) {
                                        $filePath = $state->getPathname();
                                    } elseif (method_exists($state, 'path')) {
                                        $filePath = $state->path();
                                    } elseif (method_exists($state, 'getPath')) {
                                        $filePath = $state->getPath();
                                    }

                                    if ($filePath && file_exists($filePath)) {
                                        // Store the file path for later use
                                        $set('stored_file_path', $filePath);

                                        // Detect sheets first (for Excel files)
                                        $sheets = static::getFileSheets($filePath);
                                        if (!empty($sheets)) {
                                            $set('available_sheets', $sheets);

                                            // If multiple sheets, don't auto-detect columns yet
                                            if (count($sheets) > 1) {
                                                $set('available_columns', []);
                                                return;
                                            }
                                        }

                                        // For single sheet or CSV files, detect columns
                                        $columns = static::getFileColumns($filePath);
                                        if (!empty($columns)) {
                                            $set('available_columns', $columns);
                                        }
                                    }
                                } catch (\Exception $e) {
                                    // Silently handle errors
                                }
                            }),

                        Forms\Components\Select::make('date_column')
                            ->label('Date Column')
                            ->options(function ($get) {
                                $columns = $get('available_columns') ?? [];
                                if (empty($columns)) {
                                    return [];
                                }
                                return array_combine($columns, $columns);
                            })
                            ->required()
                            ->visible(fn ($get) => !empty($get('available_columns')))
                            ->reactive()
                            ->helperText('Select the column that contains the date'),

                        Forms\Components\Select::make('numticket_column')
                            ->label('Ticket Number Column')
                            ->options(function ($get) {
                                $columns = $get('available_columns') ?? [];
                                if (empty($columns)) {
                                    return [];
                                }
                                return array_combine($columns, $columns);
                            })
                            ->required()
                            ->visible(fn ($get) => !empty($get('available_columns')))
                            ->reactive()
                            ->helperText('Select the column that contains ticket numbers (numticket)'),

                        Forms\Components\Select::make('receptionist_column')
                            ->label('Receptionist Name Column (serveur)')
                            ->options(function ($get) {
                                $columns = $get('available_columns') ?? [];
                                if (empty($columns)) {
                                    return [];
                                }
                                return array_combine($columns, $columns);
                            })
                            ->required()
                            ->visible(fn ($get) => !empty($get('available_columns')))
                            ->reactive()
                            ->helperText('Select the column that contains receptionist names (serveur)'),

                        Forms\Components\Hidden::make('available_columns')
                            ->reactive(),

                        Forms\Components\Hidden::make('available_sheets')
                            ->reactive(),

                        Forms\Components\Hidden::make('stored_file_path')
                            ->reactive(),

                        Forms\Components\Select::make('selected_sheet')
                            ->label('Select Sheet')
                            ->options(function ($get) {
                                $sheets = $get('available_sheets') ?? [];
                                if (empty($sheets)) {
                                    return [];
                                }
                                return array_combine($sheets, $sheets);
                            })
                            ->required()
                            ->visible(fn ($get) => !empty($get('available_sheets')) && count($get('available_sheets')) > 1)
                            ->reactive()
                            ->helperText('Select which sheet to import from')
                            ->afterStateUpdated(function ($state, $set, $get) {
                                // When sheet changes, re-detect columns for that sheet
                                if ($state) {
                                    try {
                                        // Use the stored file path
                                        $filePath = $get('stored_file_path');

                                        if ($filePath && file_exists($filePath)) {
                                            $columns = static::getFileColumnsFromSheet($filePath, $state);
                                            if (!empty($columns)) {
                                                $set('available_columns', $columns);
                                            }
                                        }
                                    } catch (\Exception $e) {
                                        // Silently handle errors
                                    }
                                }
                            }),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('download_csv_template')
                                ->label('Download Template')
                                ->icon('heroicon-o-arrow-down-tray')
                                ->color('success')
                                ->url(asset('storage/customer_count_template.csv'))
                                ->openUrlInNewTab(),
                        ]),
                    ])
                    ->action(function (array $data) {
                        // Validate that column mappings are provided
                        if (empty($data['date_column']) || empty($data['numticket_column']) || empty($data['receptionist_column'])) {
                            Notification::make()
                                ->title('Import Failed')
                                ->body('Please select date, ticket number, and receptionist columns.')
                                ->danger()
                                ->send();
                            return;
                        }

                        static::importFileData($data);
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('receptionist_id')
                    ->relationship('receptionist', 'full_name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('date_range')
                    ->form([
                        Forms\Components\DatePicker::make('from_date')
                            ->label('From Date'),
                        Forms\Components\DatePicker::make('to_date')
                            ->label('To Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from_date'],
                                fn (Builder $query, $date): Builder => $query->where('date', '>=', $date),
                            )
                            ->when(
                                $data['to_date'],
                                fn (Builder $query, $date): Builder => $query->where('date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->form([
                        Forms\Components\Select::make('receptionist_id')
                            ->relationship('receptionist', 'full_name')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                // Clear date when receptionist changes to avoid conflicts
                                $set('date', null);
                            }),

                        Forms\Components\DatePicker::make('date')
                            ->required()
                            ->live()
                            ->rules([
                                function (Forms\Get $get, $record) {
                                    return function (string $attribute, $value, \Closure $fail) use ($get, $record) {
                                        if (!$value || !$get('receptionist_id')) return;

                                        $date = Carbon::parse($value);
                                        $receptionistId = $get('receptionist_id');

                                        // Check for existing record on the same date (excluding current record)
                                        $existing = CustomerCount::where('receptionist_id', $receptionistId)
                                            ->where('date', $date->format('Y-m-d'))
                                            ->where('id', '!=', $record?->id)
                                            ->exists();

                                        if ($existing) {
                                            $fail('A customer count record already exists for this receptionist on this date.');
                                        }
                                    };
                                },
                            ]),

                        Forms\Components\TextInput::make('customer_count')
                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->step(1)
                            ->helperText('Total number of customers served on this date'),
                    ]),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('date', 'desc');
    }

    protected static function getFileColumns(string $filePath): array
    {
        if (!file_exists($filePath)) {
            return [];
        }

        try {
            $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

            if (in_array($fileExtension, ['xlsx', 'xls'])) {
                // For Excel files, try to use PhpSpreadsheet if available
                if (class_exists('\PhpOffice\PhpSpreadsheet\IOFactory')) {
                    try {
                        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($filePath);
                        $reader->setReadDataOnly(true);
                        $reader->setReadEmptyCells(false);
                        $spreadsheet = $reader->load($filePath);
                        $worksheet = $spreadsheet->getActiveSheet();

                        // Get the first row (header row)
                        $headerRow = $worksheet->rangeToArray('A1:Z1', null, true, false, false)[0];

                        // Filter out empty columns
                        $columns = array_filter($headerRow, function($col) {
                            return !empty(trim($col));
                        });

                        if (!empty($columns)) {
                            return array_values($columns);
                        }
                    } catch (\Exception $e) {
                        // PhpSpreadsheet failed, fall back to manual column selection
                    }
                }

                // Fallback: provide common column names for manual mapping
                return [
                    'full_name',
                    'nom_prenom',
                    'name',
                    'receptionist',
                    'employee',
                    'total',
                    'count',
                    'customer_count',
                    'customers',
                    'total_general',
                    'Column A',
                    'Column B',
                    'Column C',
                    'Column D',
                    'Column E',
                    'Column F'
                ];
            } else {
                // Read CSV file
                if (($handle = fopen($filePath, 'r')) !== false) {
                    $header = fgetcsv($handle, 1000, ',');
                    fclose($handle);
                    return $header ? array_filter($header, fn($col) => !empty(trim($col))) : [];
                }
            }
        } catch (\Exception $e) {
            // Return empty array on any error
        }

        return [];
    }

    protected static function getFileSheets(string $filePath): array
    {
        try {
            $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

            if (in_array($fileExtension, ['xlsx', 'xls'])) {
                // For Excel files, get sheet names using PhpSpreadsheet
                if (class_exists('\PhpOffice\PhpSpreadsheet\IOFactory')) {
                    try {
                        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($filePath);
                        $reader->setReadDataOnly(true);
                        $reader->setReadEmptyCells(false);
                        $spreadsheet = $reader->load($filePath);

                        $sheetNames = $spreadsheet->getSheetNames();

                        if (!empty($sheetNames)) {
                            return $sheetNames;
                        }
                    } catch (\Exception $e) {
                        // PhpSpreadsheet failed, return single sheet
                        return ['Sheet1'];
                    }
                }

                // Fallback for Excel files
                return ['Sheet1'];
            } else {
                // CSV files don't have sheets
                return [];
            }
        } catch (\Exception $e) {
            return [];
        }
    }

    protected static function getFileColumnsFromSheet(string $filePath, string $sheetName): array
    {
        try {
            $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

            if (in_array($fileExtension, ['xlsx', 'xls'])) {
                // For Excel files, get columns from specific sheet
                if (class_exists('\PhpOffice\PhpSpreadsheet\IOFactory')) {
                    try {
                        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($filePath);
                        $reader->setReadDataOnly(true);
                        $reader->setReadEmptyCells(false);
                        $spreadsheet = $reader->load($filePath);

                        // Set active sheet by name
                        $spreadsheet->setActiveSheetIndexByName($sheetName);
                        $worksheet = $spreadsheet->getActiveSheet();

                        // Get the first row (header row)
                        $headerRow = $worksheet->rangeToArray('A1:Z1', null, true, false, false)[0];

                        // Filter out empty columns
                        $columns = array_filter($headerRow, function($col) {
                            return !empty(trim($col));
                        });

                        if (!empty($columns)) {
                            return array_values($columns);
                        }
                    } catch (\Exception $e) {
                        // PhpSpreadsheet failed, fall back to manual column selection
                    }
                }

                // Fallback: provide common column names for manual mapping
                return [
                    'full_name',
                    'nom_prenom',
                    'name',
                    'receptionist',
                    'employee',
                    'total',
                    'count',
                    'customer_count',
                    'customers',
                    'total_general',
                    'Column A',
                    'Column B',
                    'Column C',
                    'Column D',
                    'Column E',
                    'Column F'
                ];
            } else {
                // For CSV files, use the regular getFileColumns method
                return static::getFileColumns($filePath);
            }
        } catch (\Exception $e) {
            return [];
        }
    }

    protected static function importFileData(array $data): void
    {
        try {
            $filePath = null;
            $importFile = $data['import_file'];

            // Handle file path - by import time, it's usually a string filename
            if (is_string($importFile)) {
                // The file is stored in storage/app/private/imports/ directory
                $filePath = storage_path('app/private/' . $importFile);
            } elseif (is_object($importFile)) {
                // Try different methods to get the file path for TemporaryUploadedFile
                if (method_exists($importFile, 'getRealPath')) {
                    $filePath = $importFile->getRealPath();
                } elseif (method_exists($importFile, 'getPathname')) {
                    $filePath = $importFile->getPathname();
                } elseif (method_exists($importFile, 'path')) {
                    $filePath = $importFile->path();
                } elseif (method_exists($importFile, 'getPath')) {
                    $filePath = $importFile->getPath();
                }
            }

            if (!$filePath || !file_exists($filePath)) {
                Notification::make()
                    ->title('Import Failed')
                    ->body('Could not access the uploaded file.')
                    ->danger()
                    ->send();
                return;
            }

            // Read file (Excel or CSV)
            $selectedSheet = $data['selected_sheet'] ?? null;
            $collection = static::readFileData($filePath, $selectedSheet);

            if ($collection->isEmpty()) {
                Notification::make()
                    ->title('Import Failed')
                    ->body('The file is empty.')
                    ->danger()
                    ->send();
                return;
            }

            $header = $collection->first();

            // Get user-selected column mappings
            $dateColumn = $data['date_column'] ?? null;
            $numticketColumn = $data['numticket_column'] ?? null;
            $receptionistColumn = $data['receptionist_column'] ?? null;

            if (!$dateColumn || !$numticketColumn || !$receptionistColumn) {
                Notification::make()
                    ->title('Import Failed')
                    ->body('Please select date, ticket number, and receptionist columns.')
                    ->danger()
                    ->send();
                return;
            }

            // Find column indexes based on user selection
            $dateIndex = array_search($dateColumn, $header);
            $numticketIndex = array_search($numticketColumn, $header);
            $receptionistIndex = array_search($receptionistColumn, $header);

            if ($dateIndex === false || $numticketIndex === false || $receptionistIndex === false) {
                $foundColumns = implode(', ', $header);
                Notification::make()
                    ->title('Import Failed')
                    ->body("Selected columns not found in file. Found columns: [{$foundColumns}].")
                    ->danger()
                    ->send();
                return;
            }

            $groupedData = [];
            $errors = [];
            $skipped = 0;

            // Process data rows (skip header) and group by date and receptionist
            foreach ($collection->slice(1) as $rowIndex => $row) {
                $dateValue = trim($row[$dateIndex] ?? '');
                $numticket = trim($row[$numticketIndex] ?? '');
                $receptionistName = trim($row[$receptionistIndex] ?? '');

                // Skip rows with empty required data
                if (empty($dateValue) || empty($receptionistName)) {
                    $skipped++;
                    continue;
                }

                try {
                    // Parse and format date (remove time if present)
                    $date = null;
                    if (is_numeric($dateValue)) {
                        // Excel date serial number
                        $date = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($dateValue)->format('Y-m-d');
                    } else {
                        // String date - try multiple formats
                        $parsedDate = \DateTime::createFromFormat('d/m/Y H:i', $dateValue);
                        if (!$parsedDate) {
                            $parsedDate = \DateTime::createFromFormat('d/m/Y', $dateValue);
                        }
                        if (!$parsedDate) {
                            $parsedDate = new \DateTime($dateValue);
                        }
                        $date = $parsedDate->format('Y-m-d');
                    }

                    // Create unique key for grouping by date and receptionist
                    $groupKey = $date . '|' . $receptionistName;

                    // Initialize group if not exists
                    if (!isset($groupedData[$groupKey])) {
                        $groupedData[$groupKey] = [
                            'date' => $date,
                            'receptionist' => $receptionistName,
                            'unique_tickets' => []
                        ];
                    }

                    // Track unique tickets for this date/receptionist combination
                    // Each unique numticket = 1 customer
                    if (!empty($numticket) && !in_array($numticket, $groupedData[$groupKey]['unique_tickets'])) {
                        $groupedData[$groupKey]['unique_tickets'][] = $numticket;
                    }

                } catch (\Exception $e) {
                    $errors[] = "Row " . ($rowIndex + 2) . ": Invalid date format - " . $e->getMessage();
                    $skipped++;
                }
            }

            $imported = 0;

            // Insert grouped data
            foreach ($groupedData as $group) {
                try {
                    // Calculate customer count from unique tickets
                    $customerCount = count($group['unique_tickets']);

                    // Skip if no customers found
                    if ($customerCount === 0) {
                        continue;
                    }

                    // Find or create receptionist by full_name
                    $receptionist = Receptionist::where('full_name', $group['receptionist'])->first();

                    if (!$receptionist) {
                        // Try partial match
                        $receptionist = Receptionist::where('full_name', 'LIKE', "%{$group['receptionist']}%")->first();
                    }

                    if (!$receptionist) {
                        $errors[] = "Receptionist '{$group['receptionist']}' not found for date {$group['date']}";
                        continue;
                    }

                    // Check for existing customer count on the same date
                    $existing = CustomerCount::where('receptionist_id', $receptionist->id)
                        ->where('date', $group['date'])
                        ->first();

                    if ($existing) {
                        $existing->update(['customer_count' => $customerCount]);
                    } else {
                        CustomerCount::create([
                            'receptionist_id' => $receptionist->id,
                            'date' => $group['date'],
                            'customer_count' => $customerCount,
                        ]);
                    }

                    $imported++;

                } catch (\Exception $e) {
                    $errors[] = "Group {$group['receptionist']}-{$group['date']}: " . $e->getMessage();
                }
            }

            // Clean up uploaded file
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            $totalRows = $collection->count() - 1; // Exclude header
            $totalTickets = array_sum(array_map(function($group) { return count($group['tickets']); }, $groupedData));
            $message = "Import complete: {$imported} records imported from {$totalRows} individual customer entries (grouped by date and receptionist). Total unique tickets processed: {$totalTickets}. {$skipped} rows skipped.";
            if (!empty($errors)) {
                $message .= " " . count($errors) . " errors occurred.";
            }

            Notification::make()
                ->title('Import Completed')
                ->body($message)
                ->success()
                ->send();

            if (!empty($errors)) {
                Notification::make()
                    ->title('Import Errors')
                    ->body(implode("\n", array_slice($errors, 0, 5)) . (count($errors) > 5 ? "\n... and " . (count($errors) - 5) . " more" : ""))
                    ->warning()
                    ->send();
            }

        } catch (\Exception $e) {
            Notification::make()
                ->title('Import Failed')
                ->body('An error occurred while importing: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected static function readFileData(string $filePath, string $sheetName = null): Collection
    {
        $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

        if (in_array($fileExtension, ['xlsx', 'xls'])) {
            // For Excel files, try to use PhpSpreadsheet
            if (class_exists('\PhpOffice\PhpSpreadsheet\IOFactory')) {
                try {
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($filePath);
                    $reader->setReadDataOnly(true);
                    $reader->setReadEmptyCells(false);
                    $spreadsheet = $reader->load($filePath);

                    // Set active sheet by name if specified
                    if ($sheetName) {
                        try {
                            $spreadsheet->setActiveSheetIndexByName($sheetName);
                        } catch (\Exception $e) {
                            // If sheet name not found, use first sheet
                        }
                    }

                    $worksheet = $spreadsheet->getActiveSheet();

                    // Get all data from the worksheet
                    $data = $worksheet->toArray(null, true, true, false);

                    // Filter out completely empty rows
                    $data = array_filter($data, function($row) {
                        return !empty(array_filter($row, function($cell) {
                            return !empty(trim($cell));
                        }));
                    });

                    return collect(array_values($data));
                } catch (\Exception $e) {
                    // PhpSpreadsheet failed, return empty collection
                    return collect();
                }
            }

            // If PhpSpreadsheet is not available, return empty collection
            return collect();
        } else {
            // Read CSV file
            $data = [];
            if (($handle = fopen($filePath, 'r')) !== false) {
                while (($row = fgetcsv($handle, 1000, ',')) !== false) {
                    $data[] = $row;
                }
                fclose($handle);
            }
            return collect($data);
        }
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomerCounts::route('/'),
        ];
    }
}
