<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $defaultSettings = [
            [
                'key' => 'brand_name',
                'value' => 'Review Tracker',
                'type' => 'string',
                'description' => 'Application brand name displayed in the admin panel'
            ],
            [
                'key' => 'brand_logo',
                'value' => 'assets/logos/logo.svg',
                'type' => 'file',
                'description' => 'Brand logo file path for light mode'
            ],
            [
                'key' => 'dark_brand_logo',
                'value' => 'assets/logos/logo-dark.svg',
                'type' => 'file',
                'description' => 'Brand logo file path for dark mode'
            ],
            [
                'key' => 'favicon',
                'value' => 'assets/icons/favicon.ico',
                'type' => 'file',
                'description' => 'Favicon file path'
            ],
            [
                'key' => 'primary_color',
                'value' => '#92541B',
                'type' => 'string',
                'description' => 'Primary theme color for the admin interface'
            ],
            [
                'key' => 'navigation_type',
                'value' => 'sidebar',
                'type' => 'string',
                'description' => 'Navigation layout type (sidebar or top)'
            ],
        ];

        foreach ($defaultSettings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
