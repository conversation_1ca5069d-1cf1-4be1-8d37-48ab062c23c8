# Troubleshooting Guide

## Common Issues and Solutions

### 1. "Call to undefined method App\Models\Department::receptionists.visits()"

**Problem**: This error occurs when trying to use nested relationship counts in Eloquent queries.

**Cause**: <PERSON><PERSON>'s `withCount()` method doesn't support dot notation for nested relationships in the way we initially tried to use it.

**Solution**: Use one of these approaches:

#### Option A: Raw SQL with Jo<PERSON> (Recommended for performance)
```php
$topDepartment = Department::select('departments.*')
    ->selectRaw('COUNT(visits.id) as total_visits')
    ->leftJoin('receptionists', 'departments.id', '=', 'receptionists.department_id')
    ->leftJoin('visits', 'receptionists.id', '=', 'visits.receptionist_id')
    ->where('visits.visited_at', '>=', $thisMonth)
    ->groupBy('departments.id', 'departments.name', 'departments.is_active', 'departments.created_at', 'departments.updated_at')
    ->orderBy('total_visits', 'desc')
    ->first();
```

#### Option B: Load relationships and calculate in PHP
```php
$topDepartment = Department::with(['receptionists.visits' => function ($query) use ($thisMonth) {
    $query->where('visited_at', '>=', $thisMonth);
}])
->get()
->map(function ($department) {
    $department->total_visits = $department->receptionists->sum(function ($receptionist) {
        return $receptionist->visits->count();
    });
    return $department;
})
->sortByDesc('total_visits')
->first();
```

#### Option C: Use whereHas with subqueries
```php
$topDepartment = Department::withCount(['receptionists as total_visits' => function ($query) use ($thisMonth) {
    $query->withCount(['visits' => function ($subQuery) use ($thisMonth) {
        $subQuery->where('visited_at', '>=', $thisMonth);
    }]);
}])
->orderBy('total_visits', 'desc')
->first();
```

### 2. QR Code Generation Issues

**Problem**: QR codes not generating or displaying incorrectly.

**Solutions**:
- Ensure `storage:link` has been run
- Check file permissions on storage directories
- Verify GD extension is installed for image processing

### 3. Badge Download Not Working

**Problem**: Badge downloads failing or returning 404.

**Solutions**:
- Run `php artisan storage:link`
- Check that the BadgeService is properly generating files
- Ensure the public/storage symlink exists

### 4. Spin Wheel Not Loading

**Problem**: Spin wheel not displaying on landing pages.

**Solutions**:
- Check that Winwheel.js is loading from CDN
- Verify that gifts exist for the department
- Check browser console for JavaScript errors

### 5. Analytics Not Updating

**Problem**: Visit and click tracking not working.

**Solutions**:
- Verify that the Visit and Click models are properly saving data
- Check that the landing page controller is calling the tracking methods
- Ensure database migrations have been run

### 6. Filament Admin Panel Issues

**Problem**: Admin panel not loading or showing errors.

**Solutions**:
- Clear cache: `php artisan cache:clear`
- Clear config: `php artisan config:clear`
- Republish assets: `php artisan filament:upgrade`

### 7. Database Migration Errors

**Problem**: Migrations failing due to foreign key constraints.

**Solutions**:
- Ensure migrations are run in the correct order
- Check that referenced tables exist before creating foreign keys
- Use `php artisan migrate:fresh --seed` for a clean start

## Performance Optimization

### 1. Database Queries
- Use eager loading with `with()` to avoid N+1 queries
- Add database indexes for frequently queried columns
- Use `select()` to limit columns when not all are needed

### 2. Caching
- Cache analytics results for better performance
- Use Redis for session and cache storage in production
- Implement query result caching for expensive operations

### 3. File Storage
- Use cloud storage (S3) for production environments
- Optimize image sizes for badges and QR codes
- Implement CDN for static assets

## Security Considerations

### 1. Input Validation
- Always validate user inputs in controllers
- Use Filament's built-in validation in admin forms
- Sanitize file uploads

### 2. Access Control
- Implement proper authentication for admin panel
- Use middleware to protect sensitive routes
- Validate receptionist URLs to prevent unauthorized access

### 3. Data Protection
- Use HTTPS in production
- Implement CSRF protection (already included)
- Sanitize data before displaying in views

## Deployment Checklist

### 1. Environment Setup
- [ ] Set `APP_ENV=production`
- [ ] Configure proper database connection
- [ ] Set up file storage (S3, etc.)
- [ ] Configure mail settings
- [ ] Set proper APP_URL

### 2. Security
- [ ] Generate new APP_KEY
- [ ] Set up SSL certificates
- [ ] Configure firewall rules
- [ ] Set proper file permissions

### 3. Performance
- [ ] Enable OPcache
- [ ] Configure Redis for caching
- [ ] Set up queue workers
- [ ] Optimize autoloader: `composer install --optimize-autoloader --no-dev`

### 4. Monitoring
- [ ] Set up error logging
- [ ] Configure application monitoring
- [ ] Set up backup procedures
- [ ] Monitor disk space for file uploads

## Getting Help

If you encounter issues not covered in this guide:

1. Check the Laravel documentation: https://laravel.com/docs
2. Review Filament documentation: https://filamentphp.com/docs
3. Search for similar issues on GitHub or Stack Overflow
4. Check the application logs in `storage/logs/laravel.log`

## Useful Commands

```bash
# Clear all caches
php artisan optimize:clear

# Regenerate optimized files
php artisan optimize

# Check application status
php artisan about

# Run tests
php artisan test

# Check for security vulnerabilities
composer audit

# Update dependencies
composer update
```
